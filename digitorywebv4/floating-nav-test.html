<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floating Navigation Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .chart-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            margin-bottom: 20px;
            padding: 20px;
            height: 400px;
            transition: box-shadow 0.2s ease;
        }

        .chart-card.chart-highlight {
            box-shadow: 0 0 20px rgba(255, 179, 102, 0.4);
            border-color: #ffb366;
            animation: pulse-highlight 2s ease-in-out;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .floating-nav {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid #e1e8ed;
            padding: 16px;
            max-width: 200px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .floating-nav.minimized {
            padding: 8px;
            max-width: 48px;
            border-radius: 50%;
        }

        .nav-container {
            position: relative;
        }

        .nav-toggle {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 32px;
            height: 32px;
            background: #ffb366;
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(255, 179, 102, 0.3);
            transition: all 0.2s ease;
            z-index: 1001;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .nav-toggle:hover {
            background: #ff9f47;
            transform: scale(1.1);
        }

        .nav-content {
            display: block;
        }

        .floating-nav.minimized .nav-content {
            display: none;
        }

        .nav-title {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            margin-bottom: 12px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .nav-button {
            font-size: 11px;
            padding: 8px 12px;
            min-height: 32px;
            border-radius: 6px;
            text-transform: none;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: all 0.2s ease;
            background-color: #ffb366;
            color: white;
            border: none;
            cursor: pointer;
        }

        .nav-button:hover {
            transform: translateX(-2px);
            box-shadow: 0 2px 8px rgba(255, 179, 102, 0.3);
            background-color: #ff9f47;
        }

        @keyframes pulse-highlight {
            0% {
                box-shadow: 0 0 20px rgba(255, 179, 102, 0.4);
            }
            50% {
                box-shadow: 0 0 30px rgba(255, 179, 102, 0.6);
            }
            100% {
                box-shadow: 0 0 20px rgba(255, 179, 102, 0.4);
            }
        }

        .table-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Reconciliation Dashboard - Floating Navigation Test</h1>
        
        <!-- Floating Navigation -->
        <div class="floating-nav" id="floatingNav">
            <div class="nav-container">
                <!-- Toggle Button -->
                <button class="nav-toggle" onclick="toggleNavigation()" title="Toggle Navigation">
                    <span id="toggleIcon">✕</span>
                </button>

                <!-- Navigation Content -->
                <div class="nav-content" id="navContent">
                    <div class="nav-title">Quick Navigation</div>
                    <div class="nav-buttons">
                        <button class="nav-button" onclick="scrollToChart('main_reconciliation_table')">Main</button>
                        <button class="nav-button" onclick="scrollToChart('workarea_transfer_table')">Workarea</button>
                        <button class="nav-button" onclick="scrollToChart('store_transfer_table')">Store</button>
                        <button class="nav-button" onclick="scrollToChart('food_cost_analysis_table')">Cost Analysis</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Cards -->
        <div class="chart-card" id="chart-main_reconciliation_table">
            <div class="chart-title">1. Reconciliation: Opening + Store Transfer + Workareas Transfer - Closing = Consumption</div>
            <div class="table-content">
                <p>This is the main reconciliation table showing the complete flow from opening stock to consumption.</p>
                <p>Formula: Opening Stock + Store Transfer + Workarea Transfer - Closing Stock = Consumption</p>
                <p>This table provides the comprehensive view of inventory movement across all departments and categories.</p>
            </div>
        </div>

        <div class="chart-card" id="chart-workarea_transfer_table">
            <div class="chart-title">2. Transfer In/Out (Workareas): Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents</div>
            <div class="table-content">
                <p>This table shows workarea-level transfers including cross-category indents.</p>
                <p>Formula: Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents = Net Transfer</p>
                <p>Cross-category indents appear here as "Goods from Other Categories' Indents" subcategories.</p>
            </div>
        </div>

        <div class="chart-card" id="chart-store_transfer_table">
            <div class="chart-title">3. Transfer In/Out (Store): Purchase + IBT In - IBT Out - Return Qty + Spoilage</div>
            <div class="table-content">
                <p>This table shows store-level transfers without cross-category indents.</p>
                <p>Formula: Purchase + IBT In - IBT Out - Return Qty + Spoilage = Net Transfer</p>
                <p>Only actual physical store transfers are shown here, no cross-category cost allocations.</p>
            </div>
        </div>

        <div class="chart-card" id="chart-food_cost_analysis_table">
            <div class="chart-title">4. Food Cost Analysis: Department-wise Cost vs Sales Analysis</div>
            <div class="table-content">
                <p>This table provides cost analysis comparing consumption costs with sales revenue.</p>
                <p>Shows department-wise profitability and cost percentages.</p>
                <p>Includes cross-category cost allocations for accurate department costing.</p>
            </div>
        </div>
    </div>

    <script>
        let isNavMinimized = false;

        function scrollToChart(chartId) {
            const element = document.getElementById('chart-' + chartId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });

                // Add highlight effect
                element.classList.add('chart-highlight');
                setTimeout(() => {
                    element.classList.remove('chart-highlight');
                }, 2000);
            }
        }

        function toggleNavigation() {
            const nav = document.getElementById('floatingNav');
            const toggleIcon = document.getElementById('toggleIcon');

            isNavMinimized = !isNavMinimized;

            if (isNavMinimized) {
                nav.classList.add('minimized');
                toggleIcon.textContent = '☰';
                nav.title = 'Expand Navigation';
            } else {
                nav.classList.remove('minimized');
                toggleIcon.textContent = '✕';
                nav.title = 'Minimize Navigation';
            }
        }
    </script>
</body>
</html>
